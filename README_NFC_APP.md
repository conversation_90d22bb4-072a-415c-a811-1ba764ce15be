# App Android per Lettura Badge NFC

## Descrizione
Questa è un'app Android sviluppata in Kotlin per la lettura di badge e tag NFC. L'app è progettata per funzionare su dispositivi Android 14+ e implementa il Foreground Dispatch System per intercettare i tag NFC quando l'app è in primo piano.

## Funzionalità Principali

### ✅ Controllo Supporto NFC
- **NFC non supportato**: Mostra messaggio "NFC non supportato dal dispositivo"
- **NFC disabilitato**: Mostra messaggio "NFC non abilitato, vai alle impostazioni e abilita NFC" con link alle impostazioni
- **NFC abilitato**: Mostra "NFC abilitato e pronto" e permette la lettura dei tag

### 📱 Interfaccia Utente
- Layout semplice e intuitivo con TextView centrate
- **Status TextView**: Mostra lo stato del supporto NFC con colori appropriati
- **Data TextView**: Visualizza i dati del tag NFC letto
- **Instructions TextView**: Fornisce istruzioni all'utente

### 🏷️ Lettura Tag NFC
L'app supporta diverse tecnologie NFC:
- **NDEF** (NFC Data Exchange Format)
- **Mifare Classic** (carte aziendali, tessere)
- **Mifare Ultralight** (tag semplici)
- **NFC-A/B/F/V** (protocolli ISO)

### 🔧 Dati Visualizzati
Per ogni tag letto, l'app mostra:
- **ID del tag** in formato esadecimale
- **Tecnologie supportate** dal tag
- **Dati NDEF** se presenti (tipo, payload, dimensione)
- **Dati Mifare** se applicabile (settori, blocchi, autenticazione)
- **Informazioni tecniche** (ATQA, SAK, Protocol Info)
- **Timestamp** della lettura

## Come Testare l'App

### Prerequisiti
1. Dispositivo Android con supporto NFC (Android 14+)
2. NFC abilitato nelle impostazioni del dispositivo
3. Tag NFC o carte con chip NFC per il test

### Scenari di Test

#### 1. Test su Dispositivo senza NFC
- Installa l'app su un dispositivo senza chip NFC
- **Risultato atteso**: "❌ NFC non supportato dal dispositivo"

#### 2. Test con NFC Disabilitato
- Installa l'app su dispositivo con NFC
- Disabilita NFC nelle impostazioni
- Apri l'app
- **Risultato atteso**: "⚠️ NFC non abilitato, vai alle impostazioni e abilita NFC"
- Tocca il messaggio per aprire le impostazioni NFC

#### 3. Test con NFC Abilitato
- Abilita NFC nelle impostazioni
- Apri l'app
- **Risultato atteso**: "✅ NFC abilitato e pronto"

#### 4. Test Lettura Tag NFC
- Con l'app aperta e NFC abilitato
- Avvicina un tag NFC al dispositivo
- **Risultato atteso**: 
  - Status cambia in "✅ Tag NFC letto con successo!"
  - Vengono visualizzati i dati del tag (ID, tecnologie, contenuto)
  - Timestamp della lettura

### Tipi di Tag Consigliati per il Test
1. **Carte di credito/debito** (spesso NFC-A con Mifare)
2. **Badge aziendali** (spesso Mifare Classic)
3. **Tag NFC programmabili** (NDEF)
4. **Tessere trasporti pubblici** (varie tecnologie)

## Struttura del Progetto

### File Principali
- `MainActivity.kt`: Logica principale dell'app
- `activity_main.xml`: Layout dell'interfaccia utente
- `AndroidManifest.xml`: Configurazione permessi e intent NFC
- `nfc_tech_filter.xml`: Tecnologie NFC supportate

### Caratteristiche Tecniche
- **Target SDK**: 36 (Android 14+)
- **Min SDK**: 34
- **Linguaggio**: Kotlin
- **Architettura**: Single Activity
- **Pattern**: Foreground Dispatch System

## Sicurezza e Limitazioni
- L'app legge solo i dati pubblici dei tag NFC
- Per Mifare Classic, usa solo chiavi di default per sicurezza
- Limita la lettura a pochi settori/pagine per evitare operazioni lunghe
- Non scrive mai sui tag (solo lettura)

## Risoluzione Problemi

### L'app non rileva i tag
1. Verifica che NFC sia abilitato
2. Assicurati che l'app sia in primo piano
3. Prova con tag diversi
4. Riavvia l'app

### Errori di lettura
- Alcuni tag potrebbero essere protetti da password
- Prova con tag diversi per verificare la funzionalità
- Controlla i log per messaggi di errore dettagliati

## Note per Sviluppatori
Il codice è ampiamente commentato per facilitare la comprensione da parte di sviluppatori con esperienza in Java e Python. Ogni funzione include documentazione dettagliata sul suo scopo e funzionamento.
