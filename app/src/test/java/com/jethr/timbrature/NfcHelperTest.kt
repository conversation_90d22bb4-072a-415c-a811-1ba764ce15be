package com.jethr.timbrature

import org.junit.Test
import org.junit.Assert.*

/**
 * Test unitari per le funzioni helper NFC
 * 
 * Questi test verificano che le funzioni di formattazione dei dati
 * funzionino correttamente con diversi input.
 */
class NfcHelperTest {

    @Test
    fun testBytesToHex_EmptyArray() {
        val bytes = byteArrayOf()
        val result = bytesToHex(bytes)
        assertEquals("Vuoto", result)
    }

    @Test
    fun testBytesToHex_SingleByte() {
        val bytes = byteArrayOf(0xA1.toByte())
        val result = bytesToHex(bytes)
        assertEquals("A1", result)
    }

    @Test
    fun testBytesToHex_MultipleBytes() {
        val bytes = byteArrayOf(0xA1.toByte(), 0xB2.toByte(), 0xC3.toByte(), 0xD4.toByte())
        val result = bytesToHex(bytes)
        assertEquals("A1 B2 C3 D4", result)
    }

    @Test
    fun testBytesToHex_ZeroBytes() {
        val bytes = byteArrayOf(0x00, 0x00, 0x00)
        val result = bytesToHex(bytes)
        assertEquals("00 00 00", result)
    }

    @Test
    fun testBytesToAscii_PrintableCharacters() {
        val bytes = "Hello".toByteArray()
        val result = bytesToAscii(bytes)
        assertEquals("Hello", result)
    }

    @Test
    fun testBytesToAscii_NonPrintableCharacters() {
        val bytes = byteArrayOf(0x01, 0x02, 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x7F.toByte())
        val result = bytesToAscii(bytes)
        assertEquals("..Hello.", result)
    }

    @Test
    fun testByteToHex() {
        assertEquals("A1", byteToHex(0xA1.toByte()))
        assertEquals("00", byteToHex(0x00))
        assertEquals("FF", byteToHex(0xFF.toByte()))
    }

    // Funzioni helper per i test (copiate dalla MainActivity)
    private fun bytesToHex(bytes: ByteArray): String {
        if (bytes.isEmpty()) return "Vuoto"
        
        return bytes.joinToString(" ") { byte ->
            "%02X".format(byte)
        }
    }

    private fun byteToHex(byte: Byte): String {
        return "%02X".format(byte)
    }

    private fun bytesToAscii(bytes: ByteArray): String {
        return bytes.map { byte ->
            if (byte in 32..126) { // Caratteri ASCII stampabili
                byte.toInt().toChar()
            } else {
                '.' // Sostituisce caratteri non stampabili con un punto
            }
        }.joinToString("")
    }
}
