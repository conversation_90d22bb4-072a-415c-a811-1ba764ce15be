<?xml version="1.0" encoding="utf-8"?>
<!-- File di configurazione per specificare le tecnologie NFC supportate dall'app -->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Supporto per diverse tecnologie NFC -->
    <tech-list>
        <!-- NfcA: ISO 14443-3A (la più comune per carte di credito, badge aziendali) -->
        <tech>android.nfc.tech.NfcA</tech>
    </tech-list>
    
    <tech-list>
        <!-- NfcB: ISO 14443-3B -->
        <tech>android.nfc.tech.NfcB</tech>
    </tech-list>
    
    <tech-list>
        <!-- NfcF: JIS 6319-4 (FeliCa) -->
        <tech>android.nfc.tech.NfcF</tech>
    </tech-list>
    
    <tech-list>
        <!-- NfcV: ISO 15693 -->
        <tech>android.nfc.tech.NfcV</tech>
    </tech-list>
    
    <tech-list>
        <!-- NDEF: NFC Data Exchange Format -->
        <tech>android.nfc.tech.Ndef</tech>
    </tech-list>
    
    <tech-list>
        <!-- NdefFormatable: Tag che possono essere formattati per NDEF -->
        <tech>android.nfc.tech.NdefFormatable</tech>
    </tech-list>
    
    <tech-list>
        <!-- MifareClassic: Tecnologia Mifare Classic -->
        <tech>android.nfc.tech.MifareClassic</tech>
    </tech-list>
    
    <tech-list>
        <!-- MifareUltralight: Tecnologia Mifare Ultralight -->
        <tech>android.nfc.tech.MifareUltralight</tech>
    </tech-list>
</resources>
