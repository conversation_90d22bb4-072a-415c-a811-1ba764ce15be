<?xml version="1.0" encoding="utf-8"?>
<!-- Layout principale per l'app di lettura NFC -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent" android:layout_height="match_parent" android:orientation="vertical" android:gravity="center" android:padding="24dp" android:background="@android:color/white" tools:context=".MainActivity">

    <!-- TextView per mostrare lo stato del supporto NFC -->
    <TextView android:id="@+id/nfc_status_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Controllo supporto NFC..." android:textSize="18sp" android:textStyle="bold" android:textColor="@android:color/black" android:gravity="center" android:layout_marginBottom="32dp" android:padding="16dp" android:background="@android:drawable/dialog_holo_light_frame" />

    <!-- TextView per mostrare i dati del tag NFC letto -->
    <TextView android:id="@+id/nfc_data_text" android:layout_width="match_parent" android:layout_height="wrap_content" android:text="Avvicina un tag NFC per leggere i dati..." android:textSize="16sp" android:textColor="@android:color/darker_gray" android:gravity="center" android:padding="16dp" android:background="@android:drawable/editbox_background" android:minHeight="200dp" android:scrollbars="vertical" android:layout_marginTop="16dp" />

    <!-- TextView per istruzioni utente -->
    <TextView android:id="@+id/instructions_text" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="💡 Tieni il dispositivo vicino al tag NFC per leggere i dati" android:textSize="14sp" android:textColor="@android:color/darker_gray" android:gravity="center" android:layout_marginTop="24dp" android:padding="12dp" />

</LinearLayout>