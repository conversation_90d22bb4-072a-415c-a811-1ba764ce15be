package com.jethr.timbrature

import android.app.PendingIntent
import android.content.Intent
import android.content.IntentFilter
import android.nfc.NfcAdapter
import android.nfc.Tag
import android.nfc.tech.*
import android.os.Bundle
import android.provider.Settings
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import java.io.IOException

/**
 * MainActivity per la lettura di tag NFC
 *
 * Questa Activity implementa la lettura di tag NFC utilizzando il Foreground Dispatch System.
 * Il Foreground Dispatch permette all'app di intercettare i tag NFC quando è in primo piano,
 * evitando che altre app possano gestire gli stessi tag.
 *
 * Funzionalità principali:
 * - Controllo del supporto NFC del dispositivo
 * - Controllo dello stato di abilitazione NFC
 * - Lettura di diversi tipi di tag NFC (NDEF, Mifare, ISO14443, ecc.)
 * - Visualizzazione dei dati in formato leggibile
 */
class MainActivity : AppCompatActivity() {

    // Adapter NFC per gestire le operazioni NFC
    private var nfcAdapter: NfcAdapter? = null

    // PendingIntent per il Foreground Dispatch
    private lateinit var pendingIntent: PendingIntent

    // Array di IntentFilter per specificare quali Intent intercettare
    private lateinit var intentFiltersArray: Array<IntentFilter>

    // Array di tecnologie NFC supportate
    private lateinit var techListsArray: Array<Array<String>>

    // Riferimenti alle TextView del layout
    private lateinit var nfcStatusText: TextView
    private lateinit var nfcDataText: TextView
    private lateinit var instructionsText: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Inizializzazione delle TextView
        initializeViews()

        // Inizializzazione dell'adapter NFC
        initializeNfc()

        // Controllo del supporto e stato NFC
        checkNfcSupport()

        // Gestione dell'Intent che ha avviato l'Activity (se contiene dati NFC)
        handleIntent(intent)
    }

    /**
     * Inizializza i riferimenti alle TextView del layout
     */
    private fun initializeViews() {
        nfcStatusText = findViewById(R.id.nfc_status_text)
        nfcDataText = findViewById(R.id.nfc_data_text)
        instructionsText = findViewById(R.id.instructions_text)
    }

    /**
     * Inizializza l'adapter NFC e configura il Foreground Dispatch
     */
    private fun initializeNfc() {
        // Ottiene l'adapter NFC del dispositivo
        nfcAdapter = NfcAdapter.getDefaultAdapter(this)

        // Crea un PendingIntent che verrà utilizzato per intercettare i tag NFC
        // Quando un tag viene rilevato, l'Intent riavvierà questa Activity
        pendingIntent = PendingIntent.getActivity(
            this, 0,
            Intent(this, javaClass).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP),
            PendingIntent.FLAG_MUTABLE
        )

        // Configura gli IntentFilter per intercettare diversi tipi di tag NFC
        val tagDetected = IntentFilter(NfcAdapter.ACTION_TAG_DISCOVERED)
        val ndefDetected = IntentFilter(NfcAdapter.ACTION_NDEF_DISCOVERED)
        val techDetected = IntentFilter(NfcAdapter.ACTION_TECH_DISCOVERED)

        try {
            ndefDetected.addDataType("*/*")
        } catch (e: IntentFilter.MalformedMimeTypeException) {
            throw RuntimeException("Errore nella configurazione del MIME type", e)
        }

        intentFiltersArray = arrayOf(tagDetected, ndefDetected, techDetected)

        // Specifica le tecnologie NFC che l'app può gestire
        techListsArray = arrayOf(
            arrayOf(NfcA::class.java.name),
            arrayOf(NfcB::class.java.name),
            arrayOf(NfcF::class.java.name),
            arrayOf(NfcV::class.java.name),
            arrayOf(Ndef::class.java.name),
            arrayOf(NdefFormatable::class.java.name),
            arrayOf(MifareClassic::class.java.name),
            arrayOf(MifareUltralight::class.java.name)
        )
    }

    /**
     * Controlla se il dispositivo supporta NFC e se è abilitato
     */
    private fun checkNfcSupport() {
        when {
            // Caso 1: NFC non supportato dal dispositivo
            nfcAdapter == null -> {
                nfcStatusText.text = "❌ NFC non supportato dal dispositivo"
                nfcStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
                nfcDataText.text = "Questo dispositivo non ha un chip NFC integrato."
                instructionsText.text = "💡 Prova con un dispositivo che supporta NFC"
            }

            // Caso 2: NFC supportato ma disabilitato
            !nfcAdapter!!.isEnabled -> {
                nfcStatusText.text = "⚠️ NFC non abilitato, vai alle impostazioni e abilita NFC"
                nfcStatusText.setTextColor(getColor(android.R.color.holo_orange_dark))
                nfcDataText.text = "NFC è disabilitato. Tocca qui per aprire le impostazioni."
                instructionsText.text = "💡 Abilita NFC nelle impostazioni del dispositivo"

                // Rende la TextView cliccabile per aprire le impostazioni NFC
                nfcDataText.setOnClickListener {
                    startActivity(Intent(Settings.ACTION_NFC_SETTINGS))
                }
            }

            // Caso 3: NFC supportato e abilitato
            else -> {
                nfcStatusText.text = "✅ NFC abilitato e pronto"
                nfcStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
                nfcDataText.text = "Avvicina un tag NFC per leggere i dati..."
                instructionsText.text = "💡 Tieni il dispositivo vicino al tag NFC per leggere i dati"
            }
        }
    }

    /**
     * Chiamato quando l'Activity viene messa in primo piano
     * Abilita il Foreground Dispatch per intercettare i tag NFC
     */
    override fun onResume() {
        super.onResume()

        // Ricontrolla lo stato NFC ogni volta che l'Activity torna in primo piano
        checkNfcSupport()

        // Abilita il Foreground Dispatch solo se NFC è supportato e abilitato
        nfcAdapter?.let { adapter ->
            if (adapter.isEnabled) {
                adapter.enableForegroundDispatch(
                    this,
                    pendingIntent,
                    intentFiltersArray,
                    techListsArray
                )
            }
        }
    }

    /**
     * Chiamato quando l'Activity va in background
     * Disabilita il Foreground Dispatch per permettere ad altre app di gestire NFC
     */
    override fun onPause() {
        super.onPause()

        // Disabilita il Foreground Dispatch
        nfcAdapter?.disableForegroundDispatch(this)
    }

    /**
     * Chiamato quando arriva un nuovo Intent (inclusi quelli NFC)
     * Questo metodo viene chiamato quando l'Activity è già in primo piano
     */
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    /**
     * Gestisce gli Intent che contengono dati NFC
     * Questo metodo viene chiamato sia in onCreate che in onNewIntent
     */
    private fun handleIntent(intent: Intent) {
        val action = intent.action

        // Controlla se l'Intent contiene un tag NFC
        if (NfcAdapter.ACTION_TAG_DISCOVERED == action ||
            NfcAdapter.ACTION_TECH_DISCOVERED == action ||
            NfcAdapter.ACTION_NDEF_DISCOVERED == action) {

            // Estrae il tag NFC dall'Intent
            val tag = intent.getParcelableExtra<Tag>(NfcAdapter.EXTRA_TAG)
            tag?.let { readNfcTag(it) }
        }
    }

    /**
     * Legge i dati da un tag NFC e li visualizza nell'interfaccia utente
     * Questo metodo prova diverse tecnologie NFC per estrarre il massimo di informazioni
     */
    private fun readNfcTag(tag: Tag) {
        val tagInfo = StringBuilder()

        // Informazioni di base del tag
        tagInfo.append("🏷️ TAG NFC RILEVATO\n")
        tagInfo.append("═══════════════════\n\n")

        // ID del tag (sempre presente)
        tagInfo.append("📋 ID Tag: ${bytesToHex(tag.id)}\n\n")

        // Tecnologie supportate dal tag
        tagInfo.append("🔧 Tecnologie supportate:\n")
        tag.techList.forEach { tech ->
            val techName = tech.substringAfterLast(".")
            tagInfo.append("  • $techName\n")
        }
        tagInfo.append("\n")

        // Prova a leggere dati NDEF se disponibili
        if (tag.techList.contains(Ndef::class.java.name)) {
            readNdefData(tag, tagInfo)
        }

        // Prova a leggere dati Mifare Classic se disponibili
        if (tag.techList.contains(MifareClassic::class.java.name)) {
            readMifareClassicData(tag, tagInfo)
        }

        // Prova a leggere dati Mifare Ultralight se disponibili
        if (tag.techList.contains(MifareUltralight::class.java.name)) {
            readMifareUltralightData(tag, tagInfo)
        }

        // Informazioni tecniche aggiuntive
        addTechnicalInfo(tag, tagInfo)

        // Aggiorna l'interfaccia utente con i dati letti
        runOnUiThread {
            nfcDataText.text = tagInfo.toString()
            nfcStatusText.text = "✅ Tag NFC letto con successo!"
            nfcStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
        }
    }

    /**
     * Legge i dati NDEF (NFC Data Exchange Format) dal tag
     * NDEF è un formato standard per memorizzare dati strutturati sui tag NFC
     */
    private fun readNdefData(tag: Tag, tagInfo: StringBuilder) {
        val ndef = Ndef.get(tag)
        if (ndef != null) {
            try {
                ndef.connect()
                val ndefMessage = ndef.ndefMessage

                tagInfo.append("📄 DATI NDEF:\n")
                tagInfo.append("  Dimensione: ${ndef.maxSize} bytes\n")
                tagInfo.append("  Scrivibile: ${if (ndef.isWritable) "Sì" else "No"}\n")

                if (ndefMessage != null) {
                    tagInfo.append("  Record trovati: ${ndefMessage.records.size}\n\n")

                    ndefMessage.records.forEachIndexed { index, record ->
                        tagInfo.append("  📝 Record ${index + 1}:\n")
                        tagInfo.append("    TNF: ${record.tnf}\n")
                        tagInfo.append("    Tipo: ${String(record.type)}\n")
                        tagInfo.append("    Payload: ${String(record.payload)}\n")
                        tagInfo.append("    Payload (HEX): ${bytesToHex(record.payload)}\n\n")
                    }
                } else {
                    tagInfo.append("  Nessun messaggio NDEF trovato\n\n")
                }

                ndef.close()
            } catch (e: IOException) {
                tagInfo.append("  ❌ Errore nella lettura NDEF: ${e.message}\n\n")
            }
        }
    }

    /**
     * Legge i dati da un tag Mifare Classic
     * Mifare Classic è una tecnologia NFC proprietaria molto comune
     */
    private fun readMifareClassicData(tag: Tag, tagInfo: StringBuilder) {
        val mifareClassic = MifareClassic.get(tag)
        if (mifareClassic != null) {
            try {
                mifareClassic.connect()

                tagInfo.append("🔐 DATI MIFARE CLASSIC:\n")
                tagInfo.append("  Tipo: ${getMifareClassicType(mifareClassic.type)}\n")
                tagInfo.append("  Dimensione: ${mifareClassic.size} bytes\n")
                tagInfo.append("  Settori: ${mifareClassic.sectorCount}\n")
                tagInfo.append("  Blocchi: ${mifareClassic.blockCount}\n\n")

                // Legge i primi settori (limitato per sicurezza)
                val maxSectorsToRead = minOf(4, mifareClassic.sectorCount)
                for (sectorIndex in 0 until maxSectorsToRead) {
                    if (mifareClassic.authenticateSectorWithKeyA(sectorIndex, MifareClassic.KEY_DEFAULT)) {
                        tagInfo.append("  📂 Settore $sectorIndex (autenticato):\n")

                        val blocksInSector = mifareClassic.getBlockCountInSector(sectorIndex)
                        for (blockIndex in 0 until blocksInSector) {
                            val blockNumber = mifareClassic.sectorToBlock(sectorIndex) + blockIndex
                            val blockData = mifareClassic.readBlock(blockNumber)
                            tagInfo.append("    Blocco $blockNumber: ${bytesToHex(blockData)}\n")
                        }
                        tagInfo.append("\n")
                    } else {
                        tagInfo.append("  🔒 Settore $sectorIndex: Accesso negato\n")
                    }
                }

                mifareClassic.close()
            } catch (e: IOException) {
                tagInfo.append("  ❌ Errore nella lettura Mifare Classic: ${e.message}\n\n")
            }
        }
    }

    /**
     * Legge i dati da un tag Mifare Ultralight
     * Mifare Ultralight è una versione semplificata di Mifare con meno memoria
     */
    private fun readMifareUltralightData(tag: Tag, tagInfo: StringBuilder) {
        val mifareUltralight = MifareUltralight.get(tag)
        if (mifareUltralight != null) {
            try {
                mifareUltralight.connect()

                tagInfo.append("💾 DATI MIFARE ULTRALIGHT:\n")
                tagInfo.append("  Tipo: ${getMifareUltralightType(mifareUltralight.type)}\n\n")

                // Legge le prime pagine (ogni pagina è di 4 bytes)
                val maxPagesToRead = 16 // Limita la lettura per sicurezza
                for (pageIndex in 0 until maxPagesToRead) {
                    try {
                        val pageData = mifareUltralight.readPages(pageIndex)
                        tagInfo.append("  Pagina $pageIndex: ${bytesToHex(pageData.sliceArray(0..3))}\n")
                    } catch (e: IOException) {
                        tagInfo.append("  Pagina $pageIndex: Non leggibile\n")
                        break
                    }
                }
                tagInfo.append("\n")

                mifareUltralight.close()
            } catch (e: IOException) {
                tagInfo.append("  ❌ Errore nella lettura Mifare Ultralight: ${e.message}\n\n")
            }
        }
    }

    /**
     * Aggiunge informazioni tecniche aggiuntive sul tag
     */
    private fun addTechnicalInfo(tag: Tag, tagInfo: StringBuilder) {
        tagInfo.append("🔧 INFORMAZIONI TECNICHE:\n")

        // Controlla le tecnologie NFC-A
        if (tag.techList.contains(NfcA::class.java.name)) {
            val nfcA = NfcA.get(tag)
            nfcA?.let {
                try {
                    it.connect()
                    tagInfo.append("  NFC-A ATQA: ${bytesToHex(it.atqa)}\n")
                    tagInfo.append("  NFC-A SAK: ${it.sak}\n")
                    it.close()
                } catch (e: IOException) {
                    tagInfo.append("  NFC-A: Errore nella lettura\n")
                }
            }
        }

        // Controlla le tecnologie NFC-B
        if (tag.techList.contains(NfcB::class.java.name)) {
            val nfcB = NfcB.get(tag)
            nfcB?.let {
                try {
                    it.connect()
                    tagInfo.append("  NFC-B Application Data: ${bytesToHex(it.applicationData)}\n")
                    tagInfo.append("  NFC-B Protocol Info: ${bytesToHex(it.protocolInfo)}\n")
                    it.close()
                } catch (e: IOException) {
                    tagInfo.append("  NFC-B: Errore nella lettura\n")
                }
            }
        }

        tagInfo.append("\n")
        tagInfo.append("⏰ Letto il: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n")
    }

    // ========================================
    // FUNZIONI HELPER PER FORMATTAZIONE DATI
    // ========================================

    /**
     * Converte un array di byte in una stringa esadecimale leggibile
     * Questa è la funzione principale per visualizzare dati binari in formato HEX
     *
     * @param bytes Array di byte da convertire
     * @return Stringa esadecimale formattata (es. "A1 B2 C3 D4")
     */
    private fun bytesToHex(bytes: ByteArray): String {
        if (bytes.isEmpty()) return "Vuoto"

        return bytes.joinToString(" ") { byte ->
            "%02X".format(byte)
        }
    }

    /**
     * Converte un singolo byte in stringa esadecimale
     * Utile per valori singoli come SAK
     */
    private fun byteToHex(byte: Byte): String {
        return "%02X".format(byte)
    }

    /**
     * Restituisce una descrizione leggibile del tipo di Mifare Classic
     * Aiuta a identificare il tipo specifico di carta/tag
     */
    private fun getMifareClassicType(type: Int): String {
        return when (type) {
            MifareClassic.TYPE_CLASSIC -> "Classic"
            MifareClassic.TYPE_PLUS -> "Plus"
            MifareClassic.TYPE_PRO -> "Pro"
            else -> "Sconosciuto ($type)"
        }
    }

    /**
     * Restituisce una descrizione leggibile del tipo di Mifare Ultralight
     */
    private fun getMifareUltralightType(type: Int): String {
        return when (type) {
            MifareUltralight.TYPE_ULTRALIGHT -> "Ultralight"
            MifareUltralight.TYPE_ULTRALIGHT_C -> "Ultralight C"
            else -> "Sconosciuto ($type)"
        }
    }

    /**
     * Converte i dati binari in stringa ASCII leggibile quando possibile
     * Filtra i caratteri non stampabili per una migliore leggibilità
     */
    private fun bytesToAscii(bytes: ByteArray): String {
        return bytes.map { byte ->
            if (byte in 32..126) { // Caratteri ASCII stampabili
                byte.toInt().toChar()
            } else {
                '.' // Sostituisce caratteri non stampabili con un punto
            }
        }.joinToString("")
    }

    /**
     * Formatta i byte sia in HEX che in ASCII per una visualizzazione completa
     * Simile ai dump esadecimali dei debugger
     */
    private fun formatHexDump(bytes: ByteArray, bytesPerLine: Int = 16): String {
        if (bytes.isEmpty()) return "Nessun dato"

        val result = StringBuilder()
        for (i in bytes.indices step bytesPerLine) {
            val lineBytes = bytes.sliceArray(i until minOf(i + bytesPerLine, bytes.size))

            // Offset in esadecimale
            result.append("%04X: ".format(i))

            // Bytes in esadecimale
            result.append(bytesToHex(lineBytes).padEnd(bytesPerLine * 3 - 1))

            // Separatore
            result.append(" | ")

            // Rappresentazione ASCII
            result.append(bytesToAscii(lineBytes))

            result.append("\n")
        }

        return result.toString()
    }
}